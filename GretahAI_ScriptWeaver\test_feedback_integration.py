#!/usr/bin/env python3
"""
Test script for the enhanced validation feedback integration feature.

This script tests the feedback loop between validation results and script generation.
"""

import sys
import os
import json
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_validation_feedback_collection():
    """Test the validation feedback collection mechanism."""
    try:
        from state_manager import StateManager
        
        # Create a StateManager instance
        state = StateManager()
        
        # Simulate validation results
        validation_results_1 = {
            "quality_score": 75,
            "syntax_valid": True,
            "issues_found": [
                {"category": "locator", "severity": "medium", "description": "Generic XPath locator used"},
                {"category": "assertion", "severity": "low", "description": "Could add more specific assertion"}
            ],
            "recommendations": [
                "Use more specific CSS selectors instead of generic XPath",
                "Add explicit wait before assertion"
            ],
            "confidence_rating": "high",
            "ready_for_execution": True
        }
        
        validation_results_2 = {
            "quality_score": 60,
            "syntax_valid": True,
            "issues_found": [
                {"category": "locator", "severity": "medium", "description": "Generic XPath locator used"},
                {"category": "wait", "severity": "high", "description": "Missing wait condition before interaction"}
            ],
            "recommendations": [
                "Use more specific CSS selectors instead of generic XPath",
                "Add WebDriverWait before clicking elements"
            ],
            "confidence_rating": "medium",
            "ready_for_execution": True
        }
        
        # Add feedback to state
        state.add_validation_feedback(validation_results_1, "TC001", "1")
        state.add_validation_feedback(validation_results_2, "TC002", "1")
        
        # Check that feedback was stored
        assert len(state.validation_feedback_history) == 2
        assert state.validation_feedback_history[0]['test_case_id'] == "TC001"
        assert state.validation_feedback_history[1]['test_case_id'] == "TC002"
        
        logger.info("✅ Validation feedback collection verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Validation feedback collection test failed: {e}")
        return False

def test_common_issues_analysis():
    """Test the common issues analysis functionality."""
    try:
        from state_manager import StateManager
        
        # Create a StateManager instance
        state = StateManager()
        
        # Add multiple validation results with common patterns
        common_validation_results = [
            {
                "quality_score": 70,
                "issues_found": [
                    {"category": "locator", "severity": "medium", "description": "Generic XPath locator used"}
                ],
                "recommendations": ["Use more specific CSS selectors instead of generic XPath"]
            },
            {
                "quality_score": 65,
                "issues_found": [
                    {"category": "locator", "severity": "medium", "description": "Generic XPath locator used"},
                    {"category": "wait", "severity": "high", "description": "Missing wait condition"}
                ],
                "recommendations": ["Use more specific CSS selectors instead of generic XPath"]
            },
            {
                "quality_score": 80,
                "issues_found": [
                    {"category": "assertion", "severity": "low", "description": "Weak assertion used"}
                ],
                "recommendations": ["Add more specific assertions"]
            }
        ]
        
        # Add feedback entries
        for i, results in enumerate(common_validation_results):
            state.add_validation_feedback(results, f"TC{i+1:03d}", "1")
        
        # Analyze common issues
        common_issues = state.get_common_validation_issues(limit=5)
        
        # Verify analysis results
        assert len(common_issues) > 0, "Should identify common issues"
        
        # Check for the most common issue (locator)
        locator_issues = [issue for issue in common_issues if 'locator' in issue.get('description', '').lower()]
        assert len(locator_issues) > 0, "Should identify locator as a common issue"
        
        # Check for the most common recommendation
        css_recommendations = [issue for issue in common_issues if 'css' in issue.get('description', '').lower()]
        assert len(css_recommendations) > 0, "Should identify CSS selector recommendation"
        
        logger.info("✅ Common issues analysis verified")
        logger.info(f"Identified {len(common_issues)} common patterns")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Common issues analysis test failed: {e}")
        return False

def test_enhanced_prompt_generation():
    """Test the enhanced prompt generation with validation guidelines."""
    try:
        from core.prompt_builder import generate_enhanced_test_script_prompt, generate_validation_guidelines
        from state_manager import StateManager
        
        # Create state with feedback history
        state = StateManager()
        
        # Add some validation feedback
        validation_results = {
            "quality_score": 70,
            "issues_found": [
                {"category": "locator", "severity": "medium", "description": "Generic XPath locator used"}
            ],
            "recommendations": ["Use more specific CSS selectors instead of generic XPath"]
        }
        state.add_validation_feedback(validation_results, "TC001", "1")
        
        # Test data for prompt generation
        test_case = {
            "Test Case ID": "TC001",
            "Test Case Objective": "Verify user can login successfully",
            "Steps": [{"Step No": "1", "Test Steps": "Login with valid credentials", "Expected Result": "User is redirected to dashboard"}]
        }
        
        step_matches = {"1": [{"locator": "#username", "strategy": "css"}]}
        test_data = {"username": "<EMAIL>", "password": "TestPassword123"}
        website_url = "https://example.com"
        step_table_entry = {"step_no": "1", "action": "Login with valid credentials", "expected": "User is redirected to dashboard"}
        
        # Generate enhanced prompt
        enhanced_prompt = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=state,
            include_validation_feedback=True
        )
        
        # Verify enhanced prompt contains validation guidelines
        assert "Validation Guidelines" in enhanced_prompt, "Should include validation guidelines"
        assert "Locator Best Practices" in enhanced_prompt, "Should include locator best practices"
        assert "CSS selectors" in enhanced_prompt, "Should include CSS selector guidance"
        assert "Wait Conditions" in enhanced_prompt, "Should include wait condition guidance"
        
        # Test validation guidelines generation
        common_issues = [
            {"type": "recommendation", "category": "locator", "description": "Use CSS selectors", "frequency": 3},
            {"type": "issue", "category": "wait", "description": "Missing wait conditions", "frequency": 2}
        ]
        
        guidelines = generate_validation_guidelines(common_issues)
        assert "Specific Improvements" in guidelines, "Should include specific improvements section"
        assert "CSS selectors" in guidelines, "Should include CSS selector recommendation"
        
        logger.info("✅ Enhanced prompt generation verified")
        logger.info(f"Enhanced prompt length: {len(enhanced_prompt)} characters")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced prompt generation test failed: {e}")
        return False

def test_feedback_loop_integration():
    """Test the complete feedback loop integration."""
    try:
        from state_manager import StateManager
        from core.prompt_builder import generate_enhanced_test_script_prompt
        
        # Create state and simulate multiple feedback cycles
        state = StateManager()
        
        # Simulate first generation cycle with poor quality
        validation_results_1 = {
            "quality_score": 50,
            "issues_found": [
                {"category": "locator", "severity": "high", "description": "Generic XPath locator used"},
                {"category": "wait", "severity": "high", "description": "No wait conditions"}
            ],
            "recommendations": [
                "Use specific CSS selectors",
                "Add WebDriverWait before interactions"
            ]
        }
        state.add_validation_feedback(validation_results_1, "TC001", "1")
        
        # Simulate second generation cycle with improved quality
        validation_results_2 = {
            "quality_score": 75,
            "issues_found": [
                {"category": "assertion", "severity": "low", "description": "Could be more specific"}
            ],
            "recommendations": [
                "Add more detailed assertions"
            ]
        }
        state.add_validation_feedback(validation_results_2, "TC002", "1")
        
        # Verify learning progression
        common_issues = state.get_common_validation_issues()
        assert len(common_issues) > 0, "Should have identified common issues"
        
        # Test that enhanced prompt includes learned guidelines
        test_case = {"Test Case ID": "TC003", "Steps": [{"Step No": "1", "Test Steps": "Test action", "Expected Result": "Expected result"}]}
        enhanced_prompt = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches={},
            test_data={},
            website_url="https://example.com",
            state=state
        )
        
        # Verify that the prompt includes guidance based on previous feedback
        assert "CSS selectors" in enhanced_prompt, "Should include CSS selector guidance from feedback"
        assert "WebDriverWait" in enhanced_prompt, "Should include wait condition guidance from feedback"
        
        logger.info("✅ Feedback loop integration verified")
        logger.info(f"Learning from {len(state.validation_feedback_history)} feedback entries")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Feedback loop integration test failed: {e}")
        return False

def main():
    """Run all feedback integration tests."""
    logger.info("🧪 Starting validation feedback integration tests...")
    
    tests = [
        ("Validation Feedback Collection", test_validation_feedback_collection),
        ("Common Issues Analysis", test_common_issues_analysis),
        ("Enhanced Prompt Generation", test_enhanced_prompt_generation),
        ("Feedback Loop Integration", test_feedback_loop_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
    
    logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All feedback integration tests passed!")
        return True
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

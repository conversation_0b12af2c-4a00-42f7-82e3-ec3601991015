"""
Stage 3: Test Case Analysis and Conversion

This module handles test case selection, analysis, and conversion to automation format.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import json
import logging
import streamlit as st

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage3")

# Import helper functions from other modules
from core.ai import convert_test_case_to_step_table
from core.ai_helpers import analyze_step_table

def stage3_convert_test_case(state):
    """Phase 3: Test Case Analysis and Conversion."""
    st.markdown("<h2 class='stage-header'>Phase 3: Test Case Analysis</h2>", unsafe_allow_html=True)

    # Initialize state variables for step-by-step workflow
    if not hasattr(state, 'selected_test_case') or state.selected_test_case is None:
        state.selected_test_case = None
    if not hasattr(state, 'selected_step') or state.selected_step is None:
        state.selected_step = None
    if not hasattr(state, 'step_elements') or state.step_elements is None:
        state.step_elements = []
    if not hasattr(state, 'step_matches') or state.step_matches is None:
        state.step_matches = {}

    # Test Case Selection Section
    st.markdown("#### Select Test Case")

    # Add test case selection dropdown
    selected_test_case = None
    if state.test_cases:
        # Ensure test_cases is a list
        if not isinstance(state.test_cases, list):
            st.error("❌ Invalid test case format")
            return

        test_case_options = []
        for tc in state.test_cases:
            if isinstance(tc, dict):
                tc_id = tc.get('Test Case ID', '')
                objective = tc.get('Test Case Objective', '')[:50]
                if tc_id and objective:
                    test_case_options.append(f"{tc_id} - {objective}...")

        if test_case_options:
            selected_option = st.selectbox(
                "Test Case",
                ["Select a test case..."] + test_case_options
            )

            if selected_option != "Select a test case...":
                selected_tc_id = selected_option.split(" - ")[0]

                selected_test_case = next(
                    (tc for tc in state.test_cases if tc.get('Test Case ID') == selected_tc_id),
                    None
                )

                if selected_test_case:
                    # Check if this is a different test case than the currently selected one
                    is_new_test_case = (not hasattr(state, 'selected_test_case') or
                                       not state.selected_test_case or
                                       state.selected_test_case.get('Test Case ID') != selected_test_case.get('Test Case ID'))

                    if is_new_test_case:
                        # Ask for confirmation if we already have a test case with progress
                        has_progress = (hasattr(state, 'step_table_json') and state.step_table_json and
                                       (hasattr(state, 'current_step_index') and state.current_step_index > 0))

                        if has_progress:
                            with st.expander("⚠️ Warning: Progress Will Be Reset", expanded=True):
                                st.warning("Changing test cases will reset all progress on the current test case.")
                                confirm = st.button("Confirm Change", key="confirm_test_case_change")

                                if not confirm:
                                    st.info("Select the same test case to continue with your current progress.")
                                    return

                        # Reset test case state with confirmation
                        state.reset_test_case_state(confirm=True, reason=f"New test case selected: {selected_test_case.get('Test Case ID')}")

                    # Store the original test case in state manager before conversion
                    state.selected_test_case = selected_test_case
                    state.original_test_case = selected_test_case.copy() if isinstance(selected_test_case, dict) else selected_test_case

                    # Display test case details in a collapsible section
                    with st.expander("Test Case Details", expanded=True):
                        test_case_details_col1, test_case_details_col2 = st.columns(2)
                        with test_case_details_col1:
                            st.markdown(f"**ID:** {selected_test_case.get('Test Case ID')}")
                            st.markdown(f"**Steps:** {len(selected_test_case.get('Steps', []))}")
                        with test_case_details_col2:
                            st.markdown(f"**Objective:** {selected_test_case.get('Test Case Objective')}")

                    # Check if conversion has already been done
                    if hasattr(state, 'step_table_markdown') and state.step_table_markdown and hasattr(state, 'conversion_done') and state.conversion_done:
                        _display_converted_test_case(state)
                    else:
                        _display_conversion_section(state, selected_test_case)

                    # Check if step table conversion is complete before proceeding
                    if not (hasattr(state, 'step_table_markdown') and state.step_table_markdown and hasattr(state, 'conversion_done') and state.conversion_done):
                        with st.expander("Next Steps", expanded=True):
                            st.info("ℹ️ Please convert the test case to continue to Phase 4")

def _display_converted_test_case(state):
    """Display the converted test case with analysis results."""
    st.success("✅ Test case converted to automation format")

    # Display UI element detection recommendation in an expander
    if hasattr(state, 'step_table_analysis') and state.step_table_analysis:
        with st.expander("Analysis Results", expanded=False):
            step_table_analysis = state.step_table_analysis
            if step_table_analysis.get("requires_ui_elements", True):
                st.info(f"🔍 UI Element Detection Recommended: {step_table_analysis.get('reason', 'For proper automation')}")
            else:
                st.success(f"✅ UI Element Detection Not Needed: {step_table_analysis.get('reason', 'No UI elements required')}")

    # Add button to re-convert if needed
    if st.button("Re-Convert Test Case", key="reconvert_to_step_table_btn"):
        state.conversion_done = False
        st.rerun()

    # Show the converted step table in an expander
    with st.expander("View Converted Step Table", expanded=True):
        # Display the step table in a more interactive way
        tab1, tab2 = st.tabs(["Markdown Format", "JSON Data"])

        with tab1:
            st.markdown(state.step_table_markdown)
            # Add a button to copy the step table to clipboard
            if st.button("Copy Markdown", key="copy_step_table_btn", help="Copy to clipboard"):
                try:
                    import pyperclip
                    pyperclip.copy(state.step_table_markdown)
                    st.success("✅ Copied to clipboard")
                except ImportError:
                    st.warning("⚠️ pyperclip module not installed")
                except Exception as e:
                    st.error(f"❌ Error: {e}")

        with tab2:
            # Use st.json for better formatting and interaction
            st.json(state.step_table_json)
            # Add a button to copy the JSON to clipboard
            if st.button("Copy JSON", key="copy_json_btn", help="Copy to clipboard"):
                try:
                    import pyperclip
                    pyperclip.copy(json.dumps(state.step_table_json, indent=2))
                    st.success("✅ Copied to clipboard")
                except ImportError:
                    st.warning("⚠️ pyperclip module not installed")
                except Exception as e:
                    st.error(f"❌ Error: {e}")

def _display_conversion_section(state, selected_test_case):
    """Display the conversion section for test case to automation format."""
    # Conversion section
    st.markdown("#### Convert to Automation Format")

    # Add button to convert test case
    convert_button = st.button("🔄 Convert Test Case",
                            key="convert_to_step_table_btn",
                            help="Convert to automation-ready format")

    if convert_button:
        with st.spinner("Converting test case..."):
            try:
                # Use the selected test case for conversion
                markdown_table, json_table = convert_test_case_to_step_table(
                    selected_test_case,
                    state.google_api_key
                )

                # Store both the markdown table and JSON table in state manager
                state.step_table_markdown = markdown_table
                state.step_table_json = json_table
                state.conversion_done = True

                # Initialize total_steps from the converted step table
                if json_table and isinstance(json_table, list):
                    state.total_steps = len(json_table)
                    logger.info(f"State change: total_steps = {state.total_steps} (initialized from step table conversion)")

                # Analyze the step table to determine if UI element detection is needed
                step_table_analysis = analyze_step_table((markdown_table, json_table))
                state.step_table_analysis = step_table_analysis

                # Display success message
                st.success("✅ Conversion successful")

                # Show a comparison between original and converted test case
                with st.expander("Compare Original vs. Converted", expanded=True):
                    comp_col1, comp_col2 = st.columns(2)
                    with comp_col1:
                        st.markdown("**Original Format**")
                        original_steps = selected_test_case.get('Steps', [])
                        for step in original_steps:
                            st.markdown(f"**Step {step.get('Step No')}:** {step.get('Test Steps')}")
                            st.markdown(f"*Expected:* {step.get('Expected Result')}")
                            st.markdown("---")

                    with comp_col2:
                        st.markdown("**Automation Format**")
                        st.markdown(state.step_table_markdown)

                # Force a rerun to show the converted view
                st.rerun()
            except Exception as e:
                st.error(f"❌ Conversion error: {e}")

#!/usr/bin/env python3
"""
Demonstration script showing the validation feedback learning system in action.

This script simulates multiple script generation cycles to show how the system
learns from validation feedback and improves prompt quality over time.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simulate_learning_cycle():
    """Simulate a complete learning cycle with multiple script generations."""
    try:
        from state_manager import StateManager
        from core.prompt_builder import generate_enhanced_test_script_prompt, generate_validation_guidelines
        
        logger.info("🎯 Starting validation feedback learning demonstration...")
        
        # Create state manager
        state = StateManager()
        
        # Sample test case data
        test_case = {
            "Test Case ID": "TC001",
            "Test Case Objective": "Verify user login functionality",
            "Steps": [
                {
                    "Step No": "1",
                    "Test Steps": "Enter username and password, then click login",
                    "Expected Result": "User should be redirected to dashboard"
                }
            ]
        }
        
        step_matches = {"1": [{"locator": "//input[@name='username']", "strategy": "xpath"}]}
        test_data = {"username": "<EMAIL>", "password": "TestPassword123"}
        website_url = "https://example.com"
        step_table_entry = {"step_no": "1", "action": "Login with credentials", "expected": "Redirect to dashboard"}
        
        # === CYCLE 1: Initial Generation (No Learning Yet) ===
        logger.info("\n🔄 CYCLE 1: Initial script generation (no learning data)")
        
        prompt_1 = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=state
        )
        
        logger.info(f"📝 Generated prompt length: {len(prompt_1)} characters")
        logger.info("📊 Learning status: No feedback history yet")
        
        # Simulate validation results for first generation (poor quality)
        validation_1 = {
            "quality_score": 45,
            "syntax_valid": True,
            "issues_found": [
                {"category": "locator", "severity": "high", "description": "Generic XPath locator used"},
                {"category": "wait", "severity": "high", "description": "No wait conditions before interactions"},
                {"category": "assertion", "severity": "medium", "description": "Weak assertion used"}
            ],
            "recommendations": [
                "Use specific CSS selectors instead of generic XPath",
                "Add WebDriverWait before clicking elements",
                "Use more specific assertions that verify expected outcomes"
            ],
            "confidence_rating": "low",
            "ready_for_execution": False
        }
        
        # Add feedback to learning system
        state.add_validation_feedback(validation_1, "TC001", "1")
        logger.info(f"❌ Validation Score: {validation_1['quality_score']}/100 - Issues found, learning initiated")
        
        # === CYCLE 2: Learning Applied ===
        logger.info("\n🔄 CYCLE 2: Script generation with initial learning")
        
        prompt_2 = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=state
        )
        
        logger.info(f"📝 Generated prompt length: {len(prompt_2)} characters")
        logger.info(f"📊 Learning status: {len(state.validation_feedback_history)} feedback entries")
        
        # Show what guidelines were added
        common_issues = state.get_common_validation_issues(limit=3)
        logger.info("🎯 Applied guidelines based on previous feedback:")
        for issue in common_issues:
            logger.info(f"   - {issue['description']}")
        
        # Simulate improved validation results
        validation_2 = {
            "quality_score": 70,
            "syntax_valid": True,
            "issues_found": [
                {"category": "assertion", "severity": "low", "description": "Could be more specific"}
            ],
            "recommendations": [
                "Add additional assertions for comprehensive validation"
            ],
            "confidence_rating": "medium",
            "ready_for_execution": True
        }
        
        state.add_validation_feedback(validation_2, "TC002", "1")
        logger.info(f"✅ Validation Score: {validation_2['quality_score']}/100 - Significant improvement!")
        
        # === CYCLE 3: Continued Learning ===
        logger.info("\n🔄 CYCLE 3: Script generation with accumulated learning")
        
        # Simulate another test case
        test_case_3 = {
            "Test Case ID": "TC003",
            "Test Case Objective": "Verify form submission",
            "Steps": [
                {
                    "Step No": "1",
                    "Test Steps": "Fill form and submit",
                    "Expected Result": "Success message displayed"
                }
            ]
        }
        
        prompt_3 = generate_enhanced_test_script_prompt(
            test_case=test_case_3,
            step_matches={"1": [{"locator": "#submit-form", "strategy": "css"}]},
            test_data={"form_data": "test data"},
            website_url=website_url,
            step_table_entry={"step_no": "1", "action": "Submit form", "expected": "Success message"},
            state=state
        )
        
        logger.info(f"📝 Generated prompt length: {len(prompt_3)} characters")
        logger.info(f"📊 Learning status: {len(state.validation_feedback_history)} feedback entries")
        
        # Show accumulated learning
        all_issues = state.get_common_validation_issues(limit=5)
        logger.info("🧠 Accumulated learning patterns:")
        for i, issue in enumerate(all_issues, 1):
            logger.info(f"   {i}. {issue['description']} (seen {issue['frequency']}x)")
        
        # Simulate excellent validation results
        validation_3 = {
            "quality_score": 92,
            "syntax_valid": True,
            "issues_found": [],
            "recommendations": [],
            "confidence_rating": "high",
            "ready_for_execution": True
        }
        
        state.add_validation_feedback(validation_3, "TC003", "1")
        logger.info(f"🎉 Validation Score: {validation_3['quality_score']}/100 - Excellent quality achieved!")
        
        # === SUMMARY ===
        logger.info("\n📈 LEARNING PROGRESSION SUMMARY:")
        logger.info(f"   Cycle 1: {validation_1['quality_score']}/100 (Initial)")
        logger.info(f"   Cycle 2: {validation_2['quality_score']}/100 (+{validation_2['quality_score'] - validation_1['quality_score']} improvement)")
        logger.info(f"   Cycle 3: {validation_3['quality_score']}/100 (+{validation_3['quality_score'] - validation_2['quality_score']} improvement)")
        
        improvement = validation_3['quality_score'] - validation_1['quality_score']
        logger.info(f"🚀 Total improvement: +{improvement} points ({improvement/validation_1['quality_score']*100:.1f}% increase)")
        
        # Show final guidelines that would be applied
        final_guidelines = generate_validation_guidelines(all_issues)
        logger.info(f"\n📋 Final validation guidelines ({len(final_guidelines)} characters):")
        logger.info("   - Comprehensive locator best practices")
        logger.info("   - Wait condition requirements")
        logger.info("   - Assertion guidelines")
        logger.info("   - Error handling patterns")
        logger.info("   - Test data integration rules")
        logger.info(f"   - {len(all_issues)} specific improvements from feedback")
        
        logger.info("\n✨ Demonstration complete! The system successfully learned from validation feedback.")
        return True
        
    except Exception as e:
        logger.error(f"❌ Demonstration failed: {e}")
        return False

def show_prompt_comparison():
    """Show a comparison between base and enhanced prompts."""
    try:
        from state_manager import StateManager
        from core.prompt_builder import generate_test_script_prompt, generate_enhanced_test_script_prompt
        
        logger.info("\n🔍 PROMPT COMPARISON DEMONSTRATION")
        
        # Create state with some learning data
        state = StateManager()
        validation_feedback = {
            "quality_score": 60,
            "issues_found": [
                {"category": "locator", "severity": "high", "description": "Generic XPath locator used"},
                {"category": "wait", "severity": "medium", "description": "Missing wait conditions"}
            ],
            "recommendations": [
                "Use specific CSS selectors instead of generic XPath",
                "Add WebDriverWait before interactions"
            ]
        }
        state.add_validation_feedback(validation_feedback, "TC001", "1")
        
        # Sample test data
        test_case = {"Test Case ID": "TC001", "Steps": [{"Step No": "1", "Test Steps": "Test action", "Expected Result": "Expected result"}]}
        step_matches = {}
        test_data = {}
        website_url = "https://example.com"
        
        # Generate base prompt
        base_prompt = generate_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            state=state
        )
        
        # Generate enhanced prompt
        enhanced_prompt = generate_enhanced_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            state=state,
            include_validation_feedback=True
        )
        
        logger.info(f"📊 Base prompt length: {len(base_prompt)} characters")
        logger.info(f"📊 Enhanced prompt length: {len(enhanced_prompt)} characters")
        logger.info(f"📈 Enhancement: +{len(enhanced_prompt) - len(base_prompt)} characters ({(len(enhanced_prompt)/len(base_prompt)-1)*100:.1f}% increase)")
        
        # Check for key enhancements
        enhancements = [
            ("Validation Guidelines", "Validation Guidelines" in enhanced_prompt),
            ("Locator Best Practices", "Locator Best Practices" in enhanced_prompt),
            ("Wait Conditions", "Wait Conditions" in enhanced_prompt),
            ("CSS Selector Guidance", "CSS selectors" in enhanced_prompt),
            ("Specific Improvements", "Specific Improvements" in enhanced_prompt)
        ]
        
        logger.info("\n✅ Enhanced prompt includes:")
        for name, included in enhancements:
            status = "✓" if included else "✗"
            logger.info(f"   {status} {name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Prompt comparison failed: {e}")
        return False

def main():
    """Run the complete demonstration."""
    logger.info("🚀 Starting Validation Feedback Learning Demonstration")
    logger.info("=" * 60)
    
    try:
        # Run learning cycle demonstration
        if not simulate_learning_cycle():
            return False
        
        # Show prompt comparison
        if not show_prompt_comparison():
            return False
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 Demonstration completed successfully!")
        logger.info("\nKey Benefits Demonstrated:")
        logger.info("✅ Automatic learning from validation feedback")
        logger.info("✅ Progressive improvement in script quality")
        logger.info("✅ Proactive inclusion of best practices")
        logger.info("✅ Reduced need for script regeneration")
        logger.info("✅ Continuous enhancement of prompt guidelines")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Demonstration failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
